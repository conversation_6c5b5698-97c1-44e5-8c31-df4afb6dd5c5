import { View, Text, TouchableOpacity } from "react-native";
import React, { useState } from "react";
import FormLayout from "@/components/formLayout";
import { router } from "expo-router";
import InputField from "@/components/InputField";
import { Countries } from "@/constants";
import SelectDropDown from "@/components/selectDropDown";
import { Picker } from "@react-native-picker/picker";
import DatePicker from "@/components/datePicker";

const PersonalDetails = () => {
    const [personalData, setPersonalData] = useState({
        countryOfBirth: "",
        stateOfBirth: "",
        cityOfBirth: "",
        dateOfBirth: new Date(),
        gender: "",
        maritalStatus: "",
        nationality: "",
        primaryPhone: "",
        secondaryPhone: "",
        primaryAddress: {
            line_1: "",
            line_2: "",
            line_3: "",
            country_id: "",
            state: "",
            city: "",
            zipCode: "",
        },
    });
    const gender = ["Male", "Female", "Others"];
    const maritalStatus = ["Single", "Married", "Divorced"];

    return (
        <FormLayout title="Personal Details">
            {/* Progress Indicator */}
            <View className="mb-8 mt-4">
                <View className="flex-row items-center justify-between mb-2">
                    <Text className="text-sm font-JakartaMedium text-secondary-600">Step 1 of 8</Text>
                    <Text className="text-sm font-JakartaMedium text-primary-500">12% Complete</Text>
                </View>
                <View className="h-2 bg-secondary-200 rounded-full">
                    <View className="h-2 bg-primary-500 rounded-full" style={{ width: '12%' }} />
                </View>
            </View>

            {/* Bio Data Section */}
            <View className="bg-white rounded-2xl p-6 mb-6 shadow-sm border border-secondary-200">
                <View className="mb-6">
                    <Text className="text-xl font-JakartaBold text-secondary-900 mb-2">
                        Personal Information
                    </Text>
                    <Text className="text-sm font-Jakarta text-secondary-600">
                        Please provide your basic personal details
                    </Text>
                </View>

                <View className="space-y-4">
                    <DatePicker
                        value={personalData.dateOfBirth}
                        required
                        label="Date of Birth"
                        onChange={(value: any) =>
                            setPersonalData({ ...personalData, dateOfBirth: value })
                        }
                    />

                    <SelectDropDown label="Gender">
                        <Picker
                            style={{ flex: 1 }}
                            selectedValue={personalData.gender}
                            onValueChange={(itemValue) =>
                                setPersonalData({ ...personalData, gender: itemValue })
                            }
                        >
                            <Picker.Item label="Select Gender" value="" />
                            {gender?.map((status, index) => (
                                <Picker.Item label={status} value={status} key={index} />
                            ))}
                        </Picker>
                    </SelectDropDown>

                    <SelectDropDown label="Country of Birth">
                        <Picker
                            style={{ flex: 1 }}
                            selectedValue={personalData.countryOfBirth}
                            onValueChange={(itemValue) =>
                                setPersonalData({ ...personalData, countryOfBirth: itemValue })
                            }
                        >
                            <Picker.Item label="Select Country" value="" />
                            {Countries?.map((country) => (
                                <Picker.Item
                                    label={country.name}
                                    value={country.id}
                                    key={country.id}
                                />
                            ))}
                        </Picker>
                    </SelectDropDown>

                    <View className="flex-row space-x-3">
                        <View className="flex-1">
                            <InputField
                                label="State of Birth"
                                containerStyle="rounded-md"
                                value={personalData.stateOfBirth}
                                onChangeText={(value) =>
                                    setPersonalData({ ...personalData, stateOfBirth: value })
                                }
                            />
                        </View>
                        <View className="flex-1">
                            <InputField
                                label="City of Birth"
                                containerStyle="rounded-md"
                                value={personalData.cityOfBirth}
                                onChangeText={(value) =>
                                    setPersonalData({ ...personalData, cityOfBirth: value })
                                }
                            />
                        </View>
                    </View>

                    <SelectDropDown label="Marital Status">
                        <Picker
                            style={{ flex: 1 }}
                            selectedValue={personalData.maritalStatus}
                            onValueChange={(itemValue) =>
                                setPersonalData({ ...personalData, maritalStatus: itemValue })
                            }
                        >
                            <Picker.Item label="Select Status" value="" />
                            {maritalStatus?.map((status, index) => (
                                <Picker.Item label={status} value={status} key={index} />
                            ))}
                        </Picker>
                    </SelectDropDown>

                    <SelectDropDown label="Nationality">
                        <Picker
                            style={{ flex: 1 }}
                            onValueChange={(itemValue) =>
                                setPersonalData({ ...personalData, nationality: itemValue })
                            }
                            selectedValue={personalData.nationality}
                        >
                            <Picker.Item label="Select Nationality" value="" />
                            {Countries?.map((country) => (
                                <Picker.Item
                                    label={country.nationality}
                                    value={country.id}
                                    key={country.id}
                                />
                            ))}
                        </Picker>
                    </SelectDropDown>
                </View>
            </View>
            {/* Address Section */}
            <View className="bg-white rounded-2xl p-6 mb-6 shadow-sm border border-secondary-200">
                <View className="mb-6">
                    <Text className="text-xl font-JakartaBold text-secondary-900 mb-2">
                        Address Information
                    </Text>
                    <Text className="text-sm font-Jakarta text-secondary-600">
                        Please provide your current residential address
                    </Text>
                </View>

                <View className="space-y-4">
                    <InputField
                        label="Address Line 1"
                        placeholder="123 Main Street"
                        value={personalData.primaryAddress.line_1}
                        onChangeText={(value) =>
                            setPersonalData({
                                ...personalData,
                                primaryAddress: {
                                    ...personalData.primaryAddress,
                                    line_1: value,
                                },
                            })
                        }
                    />

                    <InputField
                        label="Address Line 2"
                        placeholder="Apartment, suite, etc. (optional)"
                        value={personalData.primaryAddress.line_2}
                        onChangeText={(value) =>
                            setPersonalData({
                                ...personalData,
                                primaryAddress: {
                                    ...personalData.primaryAddress,
                                    line_2: value,
                                },
                            })
                        }
                    />

                    <InputField
                        label="Address Line 3"
                        placeholder="Additional address info (optional)"
                        value={personalData.primaryAddress.line_3}
                        onChangeText={(value) =>
                            setPersonalData({
                                ...personalData,
                                primaryAddress: {
                                    ...personalData.primaryAddress,
                                    line_3: value,
                                },
                            })
                        }
                    />

                    <SelectDropDown label="Country">
                        <Picker
                            style={{ flex: 1 }}
                            selectedValue={personalData.primaryAddress.country_id}
                            onValueChange={(itemValue) =>
                                setPersonalData({
                                    ...personalData,
                                    primaryAddress: {
                                        ...personalData.primaryAddress,
                                        country_id: itemValue,
                                    },
                                })
                            }
                        >
                            <Picker.Item label="Select Country" value="" />
                            {Countries?.map((country) => (
                                <Picker.Item
                                    label={country.name}
                                    value={country.id}
                                    key={country.id}
                                />
                            ))}
                        </Picker>
                    </SelectDropDown>

                    <View className="flex-row space-x-3">
                        <View className="flex-1">
                            <InputField
                                label="State"
                                placeholder="State/Province"
                                value={personalData.primaryAddress.state}
                                onChangeText={(value) =>
                                    setPersonalData({
                                        ...personalData,
                                        primaryAddress: {
                                            ...personalData.primaryAddress,
                                            state: value,
                                        },
                                    })
                                }
                            />
                        </View>
                        <View className="flex-1">
                            <InputField
                                label="City"
                                placeholder="City"
                                value={personalData.primaryAddress.city}
                                onChangeText={(value) =>
                                    setPersonalData({
                                        ...personalData,
                                        primaryAddress: { ...personalData.primaryAddress, city: value },
                                    })
                                }
                            />
                        </View>
                        <View className="flex-1">
                            <InputField
                                label="Zip Code"
                                placeholder="12345"
                                value={personalData.primaryAddress.zipCode}
                                onChangeText={(value) =>
                                    setPersonalData({
                                        ...personalData,
                                        primaryAddress: {
                                            ...personalData.primaryAddress,
                                            zipCode: value,
                                        },
                                    })
                                }
                            />
                        </View>
                    </View>
                </View>
            </View>
            {/* Navigation */}
            <View className="mt-8 mb-4">
                <TouchableOpacity
                    onPress={() => {
                        router.push("/(root)/(forms)/(gaurdianDetails)/form");
                    }}
                    className="bg-primary-500 rounded-xl py-4 px-6 shadow-lg shadow-primary-500/25 active:scale-95 transition-transform"
                >
                    <View className="flex-row items-center justify-center space-x-2">
                        <Text className="font-JakartaSemiBold text-white text-lg">
                            Continue to Guardian Details
                        </Text>
                        <View className="bg-white/20 rounded-full p-1">
                            <Text className="text-white text-sm">→</Text>
                        </View>
                    </View>
                </TouchableOpacity>

                <View className="mt-4 flex-row items-center justify-center">
                    <Text className="text-sm font-Jakarta text-secondary-500">
                        Step 1 of 8 • Guardian Details next
                    </Text>
                </View>
            </View>
        </FormLayout>
    );
};

export default PersonalDetails;
